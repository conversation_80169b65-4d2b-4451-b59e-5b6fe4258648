{"name": "de-exercise", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"framer-motion": "^12.23.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.3.3", "input-otp": "^1.4.2", "lucide-react": "^0.513.0", "react-hook-form": "^7.57.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4", "typescript": "^5", "zustand": "^5.0.5"}}