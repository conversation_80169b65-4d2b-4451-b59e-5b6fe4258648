import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Field } from "@/lib/store";
import React from "react";

interface FieldSettingPanelProps {
  currentField: Field;
  handleFieldChanges: (updates: Partial<Field>) => void;
}

const FieldSettingPanel = ({
  currentField,
  handleFieldChanges,
}: FieldSettingPanelProps) => {
  return (
    <Card className="p-4">
      <h3 className="font-medium mb-4">Settings Panel</h3>

      <div className="space-y-4">
        <div>
          <Label className="text-sm font-medium ">Label</Label>
          <Input
            id="field-label"
            value={currentField.label}
            onChange={(e) => handleFieldChanges({ label: e.target.value })}
            placeholder="Enter field label"
            className="mt-1"
          />
        </div>

        {currentField.type !== "checkbox" && (
          <div>
            <Label className="text-sm font-medium ">Placeholder</Label>
            <Input
              id="field-placeholder"
              value={currentField.placeholder || ""}
              onChange={(e) =>
                handleFieldChanges({ placeholder: e.target.value })
              }
              placeholder="Enter placeholder text"
              className="mt-1"
            />
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Checkbox
            id="field-required"
            checked={currentField.required}
            onChange={(checked) =>
              handleFieldChanges({ required: !!checked.target.checked })
            }
          />
          <Label className="text-sm font-medium ">Required field</Label>
        </div>
      </div>
    </Card>
  );
};

export default FieldSettingPanel;
