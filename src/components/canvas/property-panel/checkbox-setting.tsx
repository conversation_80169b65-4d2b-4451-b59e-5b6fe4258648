"use client";

import { Check<PERSON><PERSON><PERSON>, <PERSON> } from "@/lib/store";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface CheckboxSettingsProps {
  field: Field;
  onChange: (updates: Partial<Field>) => void;
}

export function CheckboxSettings({ field, onChange }: CheckboxSettingsProps) {
  return (
    <Card className="p-4">
      <h3 className="font-medium mb-4">Checkbox Settings</h3>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="checkbox-default"
          checked={(field as CheckboxField).defaultChecked || false}
          onChange={(e) =>
            /* eslint-disable @typescript-eslint/no-explicit-any */
            onChange({ defaultChecked: e.target.checked } as any)
          }
        />
        <Label className="text-sm font-medium">Checked by default</Label>
      </div>
    </Card>
  );
}
