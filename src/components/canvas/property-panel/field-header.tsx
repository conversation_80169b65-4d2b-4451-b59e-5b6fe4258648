import { FieldType } from "@/lib/store";
import { Settings2 } from "lucide-react";
import React from "react";

interface FieldHeaderProps {
  className?: string;
  isCollapsed?: boolean;
  fieldType?: FieldType;
  isEmpty?: boolean;
}

const FieldHeader = ({ className, isCollapsed, isEmpty }: FieldHeaderProps) => {
  return (
    <div className={className}>
      <div className="flex items-center justify-between p-4 border-b border-border h-16 shadow-sm">
        {!isCollapsed && (
          <h2 className="font-medium text-card-foreground ml-8">
            Field Properties
          </h2>
        )}
      </div>

      {!isCollapsed && isEmpty && (
        <div className="p-6 flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
              <Settings2 className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="font-medium ">No Field Selected</h3>
            <p className="text-sm">
              Select a field from the canvas to edit its properties
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default FieldHeader;
