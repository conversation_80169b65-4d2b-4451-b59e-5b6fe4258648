"use client";

import { useState, useEffect } from "react";
import { useFormStore, Field } from "@/lib/store";
import { Separator } from "@/components/ui/separator";
import FieldHeader from "./field-header";
import FieldSettingPanel from "./field-setting-panel";
import { InputSettings } from "./input-setting";
import { TextareaSettings } from "./textarea-setting";
import { CheckboxSettings } from "./checkbox-setting";
import { SelectRadioSettings } from "./select-radio-setting";

interface PropertyPanelProps {
  className?: string;
  isCollapsed?: boolean;
}

export function PropertyPanel({
  className,
  isCollapsed = false,
}: PropertyPanelProps) {
  const { selectedFieldId, getFieldById, updateField } = useFormStore();
  const selectedField = selectedFieldId ? getFieldById(selectedFieldId) : null;

  const [currentField, setLocalField] = useState<Field | null>(null);

  useEffect(() => {
    setLocalField(selectedField!);
  }, [selectedField]);

  const handleFieldChanges = (updates: Partial<Field>) => {
    if (!selectedFieldId || !currentField) return;

    const updatedField = { ...currentField, ...updates } as Field;
    setLocalField(updatedField);
    updateField(selectedFieldId, updates);
  };

  //zero state
  if (!selectedField || !currentField) {
    return (
      <FieldHeader
        className={className}
        isCollapsed={isCollapsed}
        isEmpty={true}
      />
    );
  }

  return (
    <div className={className}>
      <FieldHeader isCollapsed={isCollapsed} fieldType={currentField.type} />

      {!isCollapsed && (
        <div className="p-4 space-y-6 overflow-auto">
          <FieldSettingPanel
            currentField={currentField}
            handleFieldChanges={handleFieldChanges}
          />

          {(currentField.type === "text" ||
            currentField.type === "email" ||
            currentField.type === "number" ||
            currentField.type === "tel" ||
            currentField.type === "url" ||
            currentField.type === "password") && (
            <InputSettings field={currentField} onChange={handleFieldChanges} />
          )}

          {currentField.type === "textarea" && (
            <TextareaSettings
              field={currentField}
              onChange={handleFieldChanges}
            />
          )}

          {currentField.type === "checkbox" && (
            <CheckboxSettings
              field={currentField}
              onChange={handleFieldChanges}
            />
          )}

          {(currentField.type === "select" ||
            currentField.type === "radio") && (
            <SelectRadioSettings
              field={currentField}
              onChange={handleFieldChanges}
            />
          )}

          <Separator />

          <div className="text-xs text-gray-500 space-y-1">
            <p>
              <strong>Field ID:</strong> {currentField.id}
            </p>
            <p>
              <strong>Order:</strong> {currentField.order + 1}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
