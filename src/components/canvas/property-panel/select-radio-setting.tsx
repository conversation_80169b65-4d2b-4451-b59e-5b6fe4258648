"use client";

import { Field, RadioField } from "@/lib/store";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";

interface SelectRadioSettingsProps {
  field: Field;
  onChange: (updates: Partial<Field>) => void;
}

export function SelectRadioSettings({
  field,
  onChange,
}: SelectRadioSettingsProps) {
  const options = [...((field as RadioField).options || [])];

  const updateOption = (
    index: number,
    key: "label" | "value",
    value: string
  ) => {
    const updated = [...options];
    updated[index] = { ...updated[index], [key]: value };
    onChange({ options: updated } as Partial<Field>);
  };

  const addOption = () => {
    const nextIndex = options.length + 1;
    const updated = [
      ...options,
      { label: `Option ${nextIndex}`, value: `option${nextIndex}` },
    ];
    onChange({ options: updated } as Partial<Field>);
  };

  const removeOption = (index: number) => {
    const updated = [...options];
    updated.splice(index, 1);
    onChange({ options: updated } as Partial<Field>);
  };

  return (
    <Card className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-medium">Options</h3>
        <Button
          size="sm"
          onClick={addOption}
          className="flex items-center gap-1"
          variant="outline"
        >
          <Plus className="w-4 h-4" />
          Add Option
        </Button>
      </div>

      <div className="space-y-3">
        {options.map((option, index) => (
          <div key={index} className="flex gap-2 items-center">
            <Input
              placeholder="Option label"
              value={option.label}
              onChange={(e) => updateOption(index, "label", e.target.value)}
              className="text-sm"
            />
            <Input
              placeholder="Option value"
              value={option.value}
              onChange={(e) => updateOption(index, "value", e.target.value)}
              className="text-sm"
            />
            <Button
              size="icon"
              variant="ghost"
              onClick={() => removeOption(index)}
              className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        ))}

        {options.length === 0 && (
          <p className="text-sm text-center py-4">
            Click &quot;Add Option&quot;
          </p>
        )}
      </div>
    </Card>
  );
}
