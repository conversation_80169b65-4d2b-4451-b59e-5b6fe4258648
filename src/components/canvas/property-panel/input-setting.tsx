"use client";

import { Field, TextField } from "@/lib/store";
import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface InputSettingsProps {
  field: Field;
  onChange: (updates: Partial<Field>) => void;
}

export function InputSettings({ field, onChange }: InputSettingsProps) {
  const inputType = (field as TextField).inputType || field.type;
  /* eslint-disable @typescript-eslint/no-explicit-any */
  return (
    <Card className="p-4 gap-2">
      <h3 className="font-medium mb-1">Input Type</h3>

      <div>
        <Select
          value={inputType}
          onValueChange={(value) => onChange({ inputType: value } as any)}
        >
          <SelectTrigger className="mt-1">
            <SelectValue
              placeholder="Select input type"
              className="text-black"
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="text">Text</SelectItem>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="tel">Phone</SelectItem>
            <SelectItem value="number">Number</SelectItem>
            <SelectItem value="url">URL</SelectItem>
            <SelectItem value="password">Password</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </Card>
  );
}
