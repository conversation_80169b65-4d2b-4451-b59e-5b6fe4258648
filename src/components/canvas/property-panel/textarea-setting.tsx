"use client";

import { Field } from "@/lib/store";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface TextareaSettingsProps {
  field: Field;
  onChange: (updates: Partial<Field>) => void;
}

export function TextareaSettings({ field, onChange }: TextareaSettingsProps) {
  /* eslint-disable @typescript-eslint/no-explicit-any */
  return (
    <Card className="p-4">
      <h3 className="font-medium mb-4">Textarea Settings</h3>

      <div>
        <Label className="text-sm font-medium">Rows</Label>
        <Input
          id="textarea-rows"
          type="number"
          min="2"
          max="10"
          value={(field as any).rows || 4}
          onChange={(e) =>
            onChange({ rows: parseInt(e.target.value) || 4 } as any)
          }
          className="mt-1"
        />
      </div>
    </Card>
  );
}
