import React from "react";
import { Head<PERSON> } from "./header";
import { cn } from "@/lib/utils";
import { BookText } from "lucide-react";
import {
  DeleteConfirmation,
  ShowConfirmationProps,
  ShowUndoToastProps,
  UndoToast,
} from "./modals/delete-confirmation";

interface ZeroStateProps {
  isDragOver: boolean;
  handleDrop: (e: React.DragEvent) => void;
  handleDragOver: (e: React.DragEvent) => void;
  handleDragEnter: (e: React.DragEvent) => void;
  handleDragLeave: (e: React.DragEvent) => void;
  showConfirmation: ShowConfirmationProps | null;
  confirmDelete: () => void;
  cancelDelete: () => void;
  showUndoToast: ShowUndoToastProps | null;
  handleUndo: () => void;
  dismissUndoToast: () => void;
}

const ZeroState = ({
  isDragOver,
  handleDrop,
  handleDragOver,
  handleDragEnter,
  handleDragLeave,
  showConfirmation,
  confirmDelete,
  cancelDelete,
  showUndoToast,
  handleUndo,
  dismissUndoToast,
}: ZeroStateProps) => {
  return (
    <div className="space-y-4">
      <Header />
      <div
        className={cn(
          "flex-1 flex items-center justify-center min-h-[400px] border-2 border-dashed mx-6 rounded-lg transition-colors",
          isDragOver ? "border-blue-400 bg-blue-50" : "border-gray-300"
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
      >
        <div className="text-center space-y-4 max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto rounded-full flex items-center justify-center">
            <BookText className="w-12 h-12 " />
          </div>
          <h3 className="text-xl font-semibold ">Start Building Your Form</h3>
          <p>Drag components from the Form Components or click to add them.</p>
        </div>
      </div>

      {showConfirmation && (
        <DeleteConfirmation
          fieldId={showConfirmation.fieldId}
          fieldLabel={showConfirmation.fieldLabel}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}

      {showUndoToast && (
        <UndoToast
          fieldLabel={showUndoToast.fieldLabel}
          onUndo={handleUndo}
          onDismiss={dismissUndoToast}
        />
      )}
    </div>
  );
};

export default ZeroState;
