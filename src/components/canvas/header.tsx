"use client";

import { useState } from "react";
import {
  Eye,
  Edit3,
  Trash2,
  Download,
  Undo2,
  Redo2,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { useFormStore } from "@/lib/store";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { motion, AnimatePresence } from "framer-motion";
import { FormSettings } from "./modals/form-settings";

const Header = () => {
  const {
    isPreviewMode,
    togglePreviewMode,
    resetForm,
    fields,
    selectedFieldId,
    undo,
    redo,
    hasUndo,
    hasRedo,
    metadata,
  } = useFormStore();

  const [showFormSettings, setShowFormSettings] = useState(false);

  const isFormEmpty = fields.length === 0;

  //download json here
  const handleExportForm = () => {
    const formData = {
      fields,
      metadata,
    };

    const blob = new Blob([JSON.stringify(formData, null, 2)], {
      type: "application/json",
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "form-config.json";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <header className="flex items-center justify-between p-4  border-b border-border h-16 shadow-sm">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3">
          <h1 className="font-semibold ">{metadata.title}</h1>
          {!isPreviewMode && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              title="Form Settings"
              onClick={() => setShowFormSettings(true)}
            >
              <Settings className="w-4 h-4" />
            </Button>
          )}
        </div>
        {!isPreviewMode && selectedFieldId && (
          <span className="text-sm text-green-600 bg-green-100 px-2 py-1 rounded-md">
            Field selected
          </span>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <ThemeToggle />
        {!isFormEmpty && (
          <AnimatePresence mode="wait">
            <motion.div
              key={isPreviewMode ? "preview" : "edit"}
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              transition={{ duration: 0.2 }}
              className="flex items-center space-x-2"
            >
              {!isPreviewMode && (
                <>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.1 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={undo}
                      disabled={!hasUndo()}
                      className="flex items-center gap-1 transition-all duration-200 hover:scale-105 disabled:hover:scale-100"
                      title="Undo (Ctrl+Z)"
                    >
                      <Undo2 className="w-4 h-4" />
                    </Button>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.15 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={redo}
                      disabled={!hasRedo()}
                      className="flex items-center gap-1 transition-all duration-200 hover:scale-105 disabled:hover:scale-100"
                      title="Redo (Ctrl+Y)"
                    >
                      <Redo2 className="w-4 h-4" />
                    </Button>
                  </motion.div>
                </>
              )}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: isPreviewMode ? 0 : 0.2 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="outline"
                  size="sm"
                  onClick={togglePreviewMode}
                  className="flex items-center gap-2 transition-all duration-200 bg-primary hover:bg-primary-hover"
                >
                  <AnimatePresence mode="wait">
                    {isPreviewMode ? (
                      <div className="flex items-center gap-2">
                        <Edit3 className="w-4 h-4" />
                        Edit
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Eye className="w-4 h-4" />
                        Preview
                      </div>
                    )}
                  </AnimatePresence>
                </Button>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: isPreviewMode ? 0.1 : 0.25 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportForm}
                  className="cursor-pointer flex items-center gap-2 transition-all duration-200"
                >
                  <Download className="w-4 h-4" />
                  Export
                </Button>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: isPreviewMode ? 0.15 : 0.3 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (
                      confirm(
                        "Are you sure you want to clear the entire form? This action cannot be undone."
                      )
                    ) {
                      resetForm();
                    }
                  }}
                  className="cursor-pointer flex items-center gap-2 bg-destructive hover:bg-destructive-hover transition-all duration-200"
                >
                  <Trash2 className="w-4 h-4" />
                  Clear
                </Button>
              </motion.div>
            </motion.div>
          </AnimatePresence>
        )}
      </div>

      <FormSettings
        isOpen={showFormSettings}
        onClose={() => setShowFormSettings(false)}
      />
    </header>
  );
};

export { Header };
