import {
  CheckboxField,
  Field,
  RadioField,
  SelectField,
  TextareaField,
  TextField,
} from "@/lib/store";
import React from "react";
import { Input } from "../ui/input";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Checkbox } from "../ui/checkbox";
import { Label } from "../ui/label";
import { Card } from "../ui/card";
import { Button } from "../ui/button";
import { Copy, Trash2 } from "lucide-react";

interface FieldRendererProps {
  field: Field;
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
}

const FieldRenderer = ({
  field,
  isSelected,
  onSelect,
  onDelete,
  onDuplicate,
}: FieldRendererProps) => {
  const renderFieldInput = () => {
    const baseClasses = "w-full transition-all duration-200";

    switch (field.type) {
      case "text":
      case "email":
      case "number":
      case "tel":
      case "url":
      case "password":
        const textField = field as TextField;
        return (
          <Input
            type={textField.inputType || field.type}
            placeholder={field.placeholder}
            className={baseClasses}
            disabled
          />
        );

      case "textarea":
        return (
          <textarea
            placeholder={field.placeholder}
            rows={(field as TextareaField).rows || 4}
            className={cn(
              "flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-xs",
              baseClasses
            )}
            disabled
          />
        );

      case "select":
        const selectField = field as SelectField;
        return (
          <Select>
            <SelectTrigger className={cn(baseClasses, "pointer-events-none")}>
              <SelectValue placeholder={field.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {selectField.options?.map(
                (option: { label: string; value: string }, index: number) => (
                  <SelectItem key={index} value={option.value}>
                    {option.label}
                  </SelectItem>
                )
              )}
            </SelectContent>
          </Select>
        );

      case "checkbox":
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={field.id}
              defaultChecked={(field as CheckboxField).defaultChecked}
              disabled
            />
            <Label className="text-sm font-normal ">{field.label}</Label>
          </div>
        );

      case "radio":
        const radioField = field as RadioField;
        return (
          <div className="space-y-2">
            {radioField.options?.map(
              (option: { label: string; value: string }, index: number) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id={`${field.id}-${index}`}
                    name={field.id}
                    value={option.value}
                    className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    disabled
                  />
                  <Label className="text-sm font-normal ">{option.label}</Label>
                </div>
              )
            )}
          </div>
        );

      default:
        return (
          <Input
            placeholder="Unknown field type"
            className={baseClasses}
            disabled
          />
        );
    }
  };

  return (
    <Card
      className={cn(
        "group relative transition-all duration-200 cursor-pointer hover:shadow-md hover:scale-[1.01] transform bg-card border border-border",
        isSelected
          ? "ring-2 ring-primary ring-offset-2 shadow-lg scale-[1.01] border-primary"
          : "hover:ring-1 hover:ring-primary/30 hover:border-primary/50"
      )}
      onClick={onSelect}
    >
      <div className="p-6">
        <div
          className={cn(
            "absolute top-2 right-2 flex gap-1 transition-opacity duration-200",
            isSelected ? "opacity-100" : "opacity-0 group-hover:opacity-100"
          )}
        >
          {field.required && (
            <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-destructive/10 text-destructive border border-destructive/20">
              Required
            </span>
          )}
          <Button
            size="icon"
            variant="ghost"
            className="h-8 w-8 hover:bg-primary/10 text-primary cursor-pointer transition-all duration-200 hover:scale-110"
            onClick={(e) => {
              e.stopPropagation();
              onDuplicate();
            }}
          >
            <Copy className="h-4 w-4" />
          </Button>
          <Button
            size="icon"
            variant="ghost"
            className="h-8 w-8 hover:bg-destructive/10 text-destructive cursor-pointer transition-all duration-200 hover:scale-110"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        {field.type !== "checkbox" && (
          <Label className="block text-sm font-medium text-card-foreground mb-2">
            {field.label}
            {field.required && <span className="text-destructive ml-1">*</span>}
          </Label>
        )}

        <div className="space-y-2">{renderFieldInput()}</div>

        {isSelected && (
          <div className="absolute pointer-events-none">
            <div className="absolute top-0 left-0 w-full h-1 bg-primary rounded-t-lg" />
            <div className="absolute top-0 left-0 w-1 h-full bg-primary rounded-l-lg" />
            <div className="absolute bottom-0 left-0 w-full h-1 bg-primary rounded-b-lg" />
            <div className="absolute top-0 right-0 w-1 h-full bg-primary rounded-r-lg" />
          </div>
        )}
      </div>
    </Card>
  );
};

export default FieldRenderer;
