"use client";

import { useState } from "react";
import { useFormStore } from "@/lib/store";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { X } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface FormSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export function FormSettings({ isOpen, onClose }: FormSettingsProps) {
  const { metadata, updateFormMetadata } = useFormStore();
  const [localTitle, setLocalTitle] = useState(metadata.title);
  const [localDescription, setLocalDescription] = useState(
    metadata.description
  );

  if (!isOpen) return null;

  const handleSave = () => {
    updateFormMetadata({
      title: localTitle.trim() || "Unnamed Form",
      description: localDescription.trim(),
    });
    onClose();
  };

  const handleCancel = () => {
    setLocalTitle(metadata.title);
    setLocalDescription(metadata.description);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="max-w-md w-full p-6 space-y-6 animate-in fade-in-0 zoom-in-95 duration-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Form Settings</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        <div className="space-y-4">
          <div>
            <Label className="text-sm font-medium">Form Name</Label>
            <Input
              id="form-title"
              value={localTitle}
              onChange={(e) => setLocalTitle(e.target.value)}
              placeholder="Enter form title"
              className="mt-1"
              maxLength={100}
            />
          </div>

          <div>
            <Label className="text-sm font-medium">Form Description</Label>
            <textarea
              id="form-description"
              value={localDescription}
              onChange={(e) => setLocalDescription(e.target.value)}
              placeholder="Enter form description (optional)"
              rows={3}
              className="flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-xs focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 mt-1"
              maxLength={500}
            />
          </div>
          <Separator />
          <div className="text-xs text-gray-500 space-y-1 pt-2">
            <p>
              <strong>Created:</strong>{" "}
              {new Date(metadata.createdAt).toLocaleDateString()}
            </p>
            <p>
              <strong>Last Updated:</strong>{" "}
              {new Date(metadata.updatedAt).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className="flex gap-3 pt-2">
          <Button variant="outline" onClick={handleCancel} className="flex-1">
            Cancel
          </Button>
          <Button onClick={handleSave} className="flex-1">
            Save Changes
          </Button>
        </div>
      </Card>
    </div>
  );
}
