"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { AlertTriangle, Undo2 } from "lucide-react";
import { useFormStore } from "@/lib/store";

interface DeleteConfirmationProps {
  fieldId: string;
  fieldLabel: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export interface ShowConfirmationProps {
  fieldId: string;
  fieldLabel: string;
}

export interface ShowUndoToastProps {
  fieldLabel: string;
}

export function DeleteConfirmation({
  fieldLabel,
  onConfirm,
  onCancel,
}: DeleteConfirmationProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    setTimeout(() => {
      onConfirm();
      setIsDeleting(false);
    }, 300);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="max-w-md w-full p-6 space-y-4 animate-in fade-in-0 zoom-in-95 duration-200">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-5 h-5 text-red-600" />
          </div>
          <div>
            <h3 className="font-semibold ">Delete Field</h3>
            <p className="text-sm">This action can be undone</p>
          </div>
        </div>

        <div className="space-y-2">
          <p>Are you sure you want to delete the {fieldLabel}?</p>
        </div>

        <div className="flex gap-3 pt-2">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isDeleting}
            className="flex-1 cursor-pointer"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isDeleting}
            className="flex-1 cursor-pointer"
          >
            {isDeleting ? "Deleting..." : "Delete Field"}
          </Button>
        </div>
      </Card>
    </div>
  );
}

interface UndoToastProps {
  onUndo: () => void;
  onDismiss: () => void;
  fieldLabel: string;
}

export function UndoToast({ onUndo, onDismiss, fieldLabel }: UndoToastProps) {
  return (
    <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 animate-in slide-in-from-bottom-2 duration-300">
      <Card className="flex items-center gap-3 p-4 shadow-lg border-l-4 border-l-blue-500">
        <div className="flex items-center gap-2 text-sm">
          <span>Deleted &quot;{fieldLabel}&quot;</span>
        </div>
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={onUndo}
            className="flex items-center gap-1 h-8"
          >
            <Undo2 className="w-3 h-3" />
            Undo
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={onDismiss}
            className="h-8 px-2"
          >
            ×
          </Button>
        </div>
      </Card>
    </div>
  );
}

export function useDeleteWithUndo() {
  const [showConfirmation, setShowConfirmation] =
    useState<ShowConfirmationProps | null>(null);

  const [showUndoToast, setShowUndoToast] = useState<{
    fieldLabel: string;
  } | null>(null);

  const { deleteField, undo, hasUndo } = useFormStore();

  const requestDelete = (fieldId: string, fieldLabel: string) => {
    setShowConfirmation({ fieldId, fieldLabel });
  };

  const confirmDelete = () => {
    if (!showConfirmation) return;

    deleteField(showConfirmation.fieldId);
    setShowConfirmation(null);

    setShowUndoToast({ fieldLabel: showConfirmation.fieldLabel });

    setTimeout(() => {
      setShowUndoToast(null);
    }, 5000); //TODO: make 3sec
  };

  const cancelDelete = () => {
    setShowConfirmation(null);
  };

  const handleUndo = () => {
    undo();
    setShowUndoToast(null);
  };

  const dismissUndoToast = () => {
    setShowUndoToast(null);
  };

  return {
    showConfirmation,
    showUndoToast,
    requestDelete,
    confirmDelete,
    cancelDelete,
    handleUndo,
    dismissUndoToast,
    hasUndo: hasUndo(),
  };
}
