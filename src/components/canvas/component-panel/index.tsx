"use client";

import {
  Type,
  AlignLeft,
  ChevronDown,
  CheckSquare,
  Circle,
  Hash,
  Plus,
} from "lucide-react";
import { useFormStore, FieldType } from "@/lib/store";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { motion } from "framer-motion";

const COMPONENT_RENDERED = [
  {
    type: "text" as FieldType,
    label: "Text Input",
    icon: Type,
    defaultProps: {
      type: "text" as const,
      inputType: "text" as const,
      label: "Text Field",
      placeholder: "Enter placeholder...",
      required: false,
    },
  },
  {
    type: "number" as FieldType,
    label: "Number Input",
    icon: Hash,
    defaultProps: {
      type: "number" as const,
      label: "Number Field",
      placeholder: "Enter number...",
      required: false,
    },
  },
  {
    type: "textarea" as FieldType,
    label: "Text Area",
    icon: AlignLeft,
    defaultProps: {
      type: "textarea" as const,
      label: "Text Area",
      placeholder: "Enter your message...",
      required: false,
      rows: 4,
    },
  },
  {
    type: "select" as FieldType,
    label: "Select Dropdown",
    icon: ChevronDown,
    defaultProps: {
      type: "select" as const,
      label: "Select Option",
      placeholder: "Choose an option...",
      required: false,
      options: [
        { label: "Option 1", value: "option1" },
        { label: "Option 2", value: "option2" },
        { label: "Option 3", value: "option3" },
      ],
    },
  },
  {
    type: "checkbox" as FieldType,
    label: "Checkbox",
    icon: CheckSquare,
    defaultProps: {
      type: "checkbox" as const,
      label: "Checkbox Field",
      required: false,
      defaultChecked: false,
    },
  },
  {
    type: "radio" as FieldType,
    label: "Radio Group",
    icon: Circle,
    defaultProps: {
      type: "radio" as const,
      label: "Radio Group",
      required: false,
      options: [
        { label: "Option A", value: "optionA" },
        { label: "Option B", value: "optionB" },
        { label: "Option C", value: "optionC" },
      ],
    },
  },
];

interface ComponentPanelProps {
  className?: string;
  isCollapsed?: boolean;
}

export function ComponentPanel({
  className,
  isCollapsed = false,
}: ComponentPanelProps) {
  const { addField } = useFormStore();

  const handleAddComponent = (component: (typeof COMPONENT_RENDERED)[0]) => {
    addField(component.defaultProps);
  };

  return (
    <motion.div
      className={className}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        className="flex items-center justify-between bg-card text-card-foreground p-4 border-b border-border h-16 shadow-sm"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        {!isCollapsed && (
          <h2 className="font-medium text-card-foreground">Form Components</h2>
        )}
      </motion.div>

      {!isCollapsed && (
        <div className="p-4 space-y-3">
          <motion.p
            className="text-sm text-muted-foreground mb-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            Add Elements (Drag and Click Supported)
          </motion.p>

          {COMPONENT_RENDERED.map((component, index) => {
            const IconComponent = component.icon;

            return (
              <motion.div
                key={component.type}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.3,
                  delay: 0.3 + index * 0.05,
                  ease: [0.4, 0, 0.2, 1],
                }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card
                  className="group cursor-pointer transition-all duration-200 hover:shadow-md border border-border hover:border-primary bg-card active:cursor-grabbing transform"
                  onClick={() => handleAddComponent(component)}
                  draggable
                  onDragStart={(e) => {
                    e.dataTransfer.setData(
                      "application/json",
                      JSON.stringify(component.defaultProps)
                    );
                    e.dataTransfer.effectAllowed = "copy";
                    e.currentTarget.style.opacity = "0.5";
                  }}
                  onDragEnd={(e) => {
                    e.currentTarget.style.opacity = "1";
                  }}
                >
                  <div className="p-4">
                    <div className="flex gap-3 items-center">
                      <div className="flex-shrink-0 w-10 h-10 bg-foreground rounded-lg flex items-center justify-center group-hover:bg-background group-hover:border transition-colors">
                        <IconComponent className="w-5 h-5 text-background  group-hover:text-foreground " />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium  text-sm">
                            {component.label}
                          </h3>
                          <Button
                            size="icon"
                            variant="outline"
                            className="opacity-0 group-hover:opacity-100 transition-opacity w-6 h-6"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddComponent(component);
                            }}
                          >
                            <Plus className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>
      )}
    </motion.div>
  );
}
