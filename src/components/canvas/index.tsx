"use client";

import { useState } from "react";
import { Header } from "@/components/canvas/header";
import {
  DeleteConfirmation,
  UndoToast,
  useDeleteWithUndo,
} from "@/components/canvas/modals/delete-confirmation";
import { useKeyboardShortcuts } from "@/hooks/use-keyboard-shortcuts";
import { useFormStore } from "@/lib/store";
import { cn } from "@/lib/utils";
import PreviewMode from "./preview-mode";
import FieldRenderer from "./field-renderer";
import ZeroState from "./zero-state";
import { motion, AnimatePresence } from "framer-motion";

const FormBuilderCanvas = () => {
  const {
    fields,
    selectedFieldId,
    selectField,
    duplicateField,
    isPreviewMode,
    addField,
    reorderFields,
    metadata,
  } = useFormStore();

  const [isDragOver, setIsDragOver] = useState(false);

  const {
    showConfirmation,
    showUndoToast,
    requestDelete,
    confirmDelete,
    cancelDelete,
    handleUndo,
    dismissUndoToast,
  } = useDeleteWithUndo();

  useKeyboardShortcuts();

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    try {
      const fieldData = JSON.parse(e.dataTransfer.getData("application/json"));
      if (fieldData && fieldData.type) {
        addField(fieldData);
      }
    } catch (error) {
      console.error("Error parsing dropped data:", error);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  };

  const handleFieldDragStart = (
    e: React.DragEvent,
    fieldId: string,
    index: number
  ) => {
    e.dataTransfer.setData("text/plain", fieldId);
    e.dataTransfer.setData("application/x-field-index", index.toString());
    e.dataTransfer.effectAllowed = "move";
  };

  const handleFieldDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    e.stopPropagation();

    const dragIndex = parseInt(
      e.dataTransfer.getData("application/x-field-index")
    );

    if (!isNaN(dragIndex) && dragIndex !== dropIndex) {
      reorderFields(dragIndex, dropIndex);
    }
  };

  const handleFieldDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = "move";
  };

  //zero state mode
  if (fields.length === 0) {
    return (
      <ZeroState
        showConfirmation={showConfirmation}
        confirmDelete={confirmDelete}
        cancelDelete={cancelDelete}
        showUndoToast={showUndoToast}
        handleUndo={handleUndo}
        dismissUndoToast={dismissUndoToast}
        isDragOver={isDragOver}
        handleDrop={handleDrop}
        handleDragOver={handleDragOver}
        handleDragEnter={handleDragEnter}
        handleDragLeave={handleDragLeave}
      />
    );
  }

  //form preview mode
  if (isPreviewMode) {
    return <PreviewMode fields={fields} metadata={metadata} />;
  }

  //form mode
  return (
    <motion.div
      className="space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Header />
      <motion.div
        className={cn(
          "p-6 space-y-6 transition-colors duration-300",
          isDragOver
            ? "bg-primary/5 border-2 border-dashed border-primary"
            : "",
          fields.length === 0 ? "min-h-[400px]" : "min-h-[400px]"
        )}
        // dynamic height increase to increase drop area
        style={{
          minHeight:
            fields.length > 0
              ? `${Math.max(400, fields.length * 120 + 200)}px`
              : "400px",
        }}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        animate={{
          backgroundColor: isDragOver ? "var(--primary)" : "transparent",
          // dynamic height increase to increase drop area
          minHeight:
            fields.length > 0
              ? `${Math.max(400, fields.length * 120 + 200)}px`
              : "600px",
        }}
        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
      >
        <AnimatePresence mode="popLayout">
          {fields
            .sort((a, b) => a.order - b.order)
            .map((field, index) => (
              <motion.div
                key={field.id}
                layout
                initial={{ opacity: 0, y: 20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{
                  opacity: 0,
                  y: -20,
                  scale: 0.95,
                  transition: { duration: 0.2 },
                }}
                transition={{
                  duration: 0.3,
                  ease: [0.4, 0, 0.2, 1],
                  layout: { duration: 0.3 },
                }}
                whileHover={{ scale: 1.01 }}
                className="relative cursor-move"
              >
                <div
                  draggable={!isPreviewMode}
                  onDragStart={(e) => handleFieldDragStart(e, field.id, index)}
                  onDrop={(e) => handleFieldDrop(e, index)}
                  onDragOver={handleFieldDragOver}
                  className="w-full"
                >
                  <FieldRenderer
                    field={field}
                    isSelected={selectedFieldId === field.id && !isPreviewMode}
                    onSelect={() => !isPreviewMode && selectField(field.id)}
                    onDelete={() => requestDelete(field.id, field.label)}
                    onDuplicate={() => duplicateField(field.id)}
                  />

                  {/* drag hands */}
                  {!isPreviewMode && (
                    <div className="absolute left-2 top-1/2 transform -translate-y-1/2 transition-opacity">
                      <div className="w-1 h-6 bg-gray-400 rounded-full cursor-grab active:cursor-grabbing"></div>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
        </AnimatePresence>
      </motion.div>

      {showConfirmation && (
        <DeleteConfirmation
          fieldId={showConfirmation.fieldId}
          fieldLabel={showConfirmation.fieldLabel}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}

      {showUndoToast && (
        <UndoToast
          fieldLabel={showUndoToast.fieldLabel}
          onUndo={handleUndo}
          onDismiss={dismissUndoToast}
        />
      )}
    </motion.div>
  );
};

export { FormBuilderCanvas };
