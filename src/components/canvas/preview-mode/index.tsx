import React from "react";

import { cn } from "@/lib/utils";
import {
  CheckboxField,
  Field,
  FormMetadata,
  RadioField,
  SelectField,
  TextareaField,
  TextField,
} from "@/lib/store";
import { motion, AnimatePresence } from "framer-motion";
import { Head<PERSON> } from "../header";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface PreviewModeProps {
  fields: Field[];
  metadata: FormMetadata;
}

const PreviewMode = ({ metadata, fields }: PreviewModeProps) => {
  return (
    <motion.div
      className="space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.4 }}
    >
      <Header />
      <motion.div
        className="p-6"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.4, delay: 0.1 }}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <Card className="max-w-2xl mx-auto p-8 shadow-lg border-0 bg-card">
            <div className="space-y-6">
              <motion.div
                className="text-center border-b border-border pb-6"
                initial={{ y: -10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              >
                <h2 className="text-2xl font-bold text-card-foreground">
                  {metadata.title}
                </h2>
                {metadata.description && (
                  <p className="text-muted-foreground mt-2">
                    {metadata.description}
                  </p>
                )}
                {!metadata.description && (
                  <p className="text-muted-foreground mt-2 text-sm italic">
                    No description provided
                  </p>
                )}
              </motion.div>

              <form className="space-y-6">
                <AnimatePresence>
                  {fields
                    .sort((a, b) => a.order - b.order)
                    .map((field, index) => {
                      const renderPreviewField = () => {
                        const baseClasses = "w-full";

                        switch (field.type) {
                          case "text":
                          case "email":
                          case "number":
                          case "tel":
                          case "url":
                          case "password":
                            const textField = field as TextField;
                            return (
                              <Input
                                type={textField.inputType || field.type}
                                placeholder={field.placeholder}
                                className={baseClasses}
                                required={field.required}
                              />
                            );

                          case "textarea":
                            return (
                              <textarea
                                placeholder={field.placeholder}
                                rows={(field as TextareaField).rows || 4}
                                className={cn(
                                  "text-black flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                                  baseClasses
                                )}
                                required={field.required}
                              />
                            );

                          case "select":
                            const selectField = field as SelectField;
                            return (
                              <Select>
                                <SelectTrigger className={baseClasses}>
                                  <SelectValue
                                    placeholder={field.placeholder}
                                  />
                                </SelectTrigger>
                                <SelectContent>
                                  {selectField.options?.map(
                                    (
                                      option: { label: string; value: string },
                                      index: number
                                    ) => (
                                      <SelectItem
                                        key={index}
                                        value={option.value}
                                      >
                                        {option.label}
                                      </SelectItem>
                                    )
                                  )}
                                </SelectContent>
                              </Select>
                            );

                          case "checkbox":
                            return (
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id={field.id}
                                  defaultChecked={
                                    (field as CheckboxField).defaultChecked
                                  }
                                  required={field.required}
                                />
                                <Label
                                  htmlFor={field.id}
                                  className="text-sm font-normal"
                                >
                                  {field.label}
                                  {field.required && (
                                    <span className="text-red-500 ml-1">*</span>
                                  )}
                                </Label>
                              </div>
                            );

                          case "radio":
                            const radioField = field as RadioField;
                            return (
                              <div className="space-y-2">
                                {radioField.options?.map(
                                  (
                                    option: { label: string; value: string },
                                    index: number
                                  ) => (
                                    <div
                                      key={index}
                                      className="flex items-center space-x-2"
                                    >
                                      <input
                                        type="radio"
                                        id={`${field.id}-${index}`}
                                        name={field.id}
                                        value={option.value}
                                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                        required={field.required && index === 0}
                                      />
                                      <Label
                                        htmlFor={`${field.id}-${index}`}
                                        className="text-sm font-normal text-black"
                                      >
                                        {option.label}
                                      </Label>
                                    </div>
                                  )
                                )}
                              </div>
                            );

                          default:
                            return (
                              <Input
                                placeholder="Unknown field type"
                                className={baseClasses}
                              />
                            );
                        }
                      };

                      return (
                        <motion.div
                          key={field.id}
                          className="space-y-2"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{
                            duration: 0.3,
                            delay: index * 0.1,
                            ease: [0.4, 0, 0.2, 1],
                          }}
                        >
                          {field.type !== "checkbox" && (
                            <Label className="text-sm font-medium text-card-foreground">
                              {field.label}
                              {field.required && (
                                <span className="text-destructive ml-1">*</span>
                              )}
                            </Label>
                          )}
                          {renderPreviewField()}
                        </motion.div>
                      );
                    })}
                </AnimatePresence>

                <div className="pt-6 border-t">
                  <Button type="submit" className="w-full" variant={"outline"}>
                    Submit Form
                  </Button>
                </div>
              </form>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default PreviewMode;
