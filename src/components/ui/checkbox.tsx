"use client";

import { CheckIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

interface CheckboxProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "type"> {
  className?: string;
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, ...props }, ref) => {
    return (
      <div className="relative inline-flex items-center ">
        <input
          type="checkbox"
          ref={ref}
          data-slot="checkbox"
          className={cn(
            "peer dark:bg-input/80 checked:bg-background checked:text-background dark:checked:bg-background checked:border-foreground focus-visible:border-ring focus-visible:ring-ring/50 size-4 shrink-0 rounded-[4px] border border-border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] cursor-pointer disabled:opacity-50 appearance-none",
            className
          )}
          {...props}
        />
        <CheckIcon className="absolute size-3.5 pointer-events-none left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 peer-checked:opacity-100 transition-opacity" />
      </div>
    );
  }
);
Checkbox.displayName = "Checkbox";

export { Checkbox };
