import { create } from "zustand";
import { v4 as uuidv4 } from "uuid";

export type FieldType =
  | "text"
  | "textarea"
  | "select"
  | "checkbox"
  | "radio"
  | "number"
  | "email"
  | "tel"
  | "url"
  | "password";

export type InputType =
  | "text"
  | "email"
  | "tel"
  | "number"
  | "url"
  | "password";

interface BaseField {
  id: string;
  type: FieldType;
  label: string;
  placeholder?: string;
  required: boolean;
  order: number;
}

export interface TextField extends BaseField {
  type: "text" | "email" | "number" | "tel" | "url" | "password";
  inputType?: InputType;
}

export interface TextareaField extends BaseField {
  type: "textarea";
  rows?: number;
}

export interface SelectField extends BaseField {
  type: "select";
  options: { label: string; value: string }[];
}

export interface CheckboxField extends BaseField {
  type: "checkbox";
  defaultChecked?: boolean;
}

export interface RadioField extends BaseField {
  type: "radio";
  options: { label: string; value: string }[];
}

export type Field =
  | TextField
  | TextareaField
  | SelectField
  | CheckboxField
  | RadioField;

interface UndoState {
  action: string;
  field?: Field;
  fields?: Field[];
  timestamp: number;
}

export interface FormMetadata {
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

interface FormState {
  fields: Field[];
  selectedFieldId: string | null;
  isPreviewMode: boolean;
  undoStack: UndoState[];
  redoStack: UndoState[];
  metadata: FormMetadata;

  addField: (field: Omit<Field, "id" | "order">) => void;
  updateField: (id: string, updates: Partial<Field>) => void;
  deleteField: (id: string) => void;
  reorderFields: (startIndex: number, endIndex: number) => void;
  duplicateField: (id: string) => void;
  selectField: (id: string | null) => void;
  togglePreviewMode: () => void;
  updateFormMetadata: (metadata: Partial<FormMetadata>) => void;
  undo: () => void;
  redo: () => void;
  hasUndo: () => boolean;
  hasRedo: () => boolean;
  resetForm: () => void;
  getFieldById: (id: string) => Field | undefined;
}

const generateId = () => `field_${uuidv4()}`;

export const useFormStore = create<FormState>((set, get) => ({
  fields: [],
  selectedFieldId: null,
  isPreviewMode: false,
  undoStack: [],
  redoStack: [],
  metadata: {
    title: "Unnamed Form",
    description: "",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },

  addField: (fieldData) =>
    set((state) => {
      const newField: Field = {
        ...fieldData,
        id: generateId(),
        order: state.fields.length,
      } as Field;

      return {
        fields: [...state.fields, newField],
        selectedFieldId: newField.id,
      };
    }),

  updateField: (id, updates) =>
    set((state) => ({
      fields: state.fields.map((field) =>
        field.id === id ? ({ ...field, ...updates } as Field) : field
      ),
    })),

  deleteField: (id) =>
    set((state) => {
      const fieldToDelete = state.fields.find((field) => field.id === id);
      if (!fieldToDelete) return state;

      const newUndoStack = [
        ...state.undoStack,
        {
          action: "delete",
          field: fieldToDelete,
          timestamp: Date.now(),
        },
      ];

      return {
        ...state,
        fields: state.fields
          .filter((field) => field.id !== id)
          .map((field, index) => ({ ...field, order: index })),
        selectedFieldId:
          state.selectedFieldId === id ? null : state.selectedFieldId,
        undoStack: newUndoStack,
        redoStack: [],
      };
    }),

  reorderFields: (startIndex, endIndex) =>
    set((state) => {
      const newFields = [...state.fields];
      const [removed] = newFields.splice(startIndex, 1);
      newFields.splice(endIndex, 0, removed);

      return {
        fields: newFields.map((field, index) => ({ ...field, order: index })),
      };
    }),

  duplicateField: (id) =>
    set((state) => {
      const fieldToDuplicate = state.fields.find((field) => field.id === id);
      if (!fieldToDuplicate) return state;

      const duplicatedField: Field = {
        ...fieldToDuplicate,
        id: generateId(),
        label: `${fieldToDuplicate.label} (Copy)`,
        order: state.fields.length,
      };

      return {
        fields: [...state.fields, duplicatedField],
        selectedFieldId: duplicatedField.id,
      };
    }),

  selectField: (id) => set({ selectedFieldId: id }),

  togglePreviewMode: () =>
    set((state) => ({
      isPreviewMode: !state.isPreviewMode,
      selectedFieldId: state.isPreviewMode ? state.selectedFieldId : null,
    })),

  resetForm: () =>
    set({
      fields: [],
      selectedFieldId: null,
      isPreviewMode: false,
    }),

  getFieldById: (id) => {
    const state = get();
    return state.fields.find((field) => field.id === id);
  },

  updateFormMetadata: (updates) =>
    set((state) => ({
      metadata: {
        ...state.metadata,
        ...updates,
        updatedAt: new Date().toISOString(),
      },
    })),

  undo: () =>
    set((state) => {
      if (state.undoStack.length === 0) return state;

      const lastAction = state.undoStack[state.undoStack.length - 1];
      const newUndoStack = state.undoStack.slice(0, -1);
      const newRedoStack = [
        ...state.redoStack,
        {
          action: "undo",
          fields: state.fields,
          timestamp: Date.now(),
        },
      ];

      if (lastAction.action === "delete" && lastAction.field) {
        const restoredFields = [...state.fields];
        restoredFields.splice(lastAction.field.order, 0, lastAction.field);
        return {
          ...state,
          fields: restoredFields.map((field, index) => ({
            ...field,
            order: index,
          })),
          undoStack: newUndoStack,
          redoStack: newRedoStack,
        };
      } else if (lastAction.fields) {
        return {
          ...state,
          fields: lastAction.fields,
          undoStack: newUndoStack,
          redoStack: newRedoStack,
        };
      }

      return state;
    }),

  redo: () =>
    set((state) => {
      if (state.redoStack.length === 0) return state;

      const lastRedo = state.redoStack[state.redoStack.length - 1];
      const newRedoStack = state.redoStack.slice(0, -1);
      const newUndoStack = [
        ...state.undoStack,
        {
          action: "redo",
          fields: state.fields,
          timestamp: Date.now(),
        },
      ];

      if (lastRedo.fields) {
        return {
          ...state,
          fields: lastRedo.fields,
          undoStack: newUndoStack,
          redoStack: newRedoStack,
        };
      }

      return state;
    }),

  hasUndo: () => {
    const state = get();
    return state.undoStack.length > 0;
  },

  hasRedo: () => {
    const state = get();
    return state.redoStack.length > 0;
  },
}));
