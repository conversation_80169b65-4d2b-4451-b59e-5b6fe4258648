import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function Home() {
  return (
    <main className="min-h-screen flex justify-center items-center">
      <div className="text-center space-y-8">
        <div className="space-y-4">
          <h1 className="text-5xl font-bold">
            FormKit
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              {" "}
              GUI Builder
            </span>
          </h1>
          <p className="text-xl max-w-2xl mx-auto">
            Create dynamic form now 🥳
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link href="/builder">
            <Button
              size="lg"
              className="flex items-center gap-2 px-8 py-3 text-lg"
              variant="secondary"
            >
              Start Building
              <ArrowRight className="w-5 h-5" />
            </Button>
          </Link>
        </div>
      </div>
    </main>
  );
}
