@import "tailwindcss";

@font-face {
  font-family: "CircularStd";
  font-weight: 400;
  src: url("https://assets.stablemoney.in/fonts/CircularStd-Book.woff")
    format("woff");
  font-display: swap;
}

@font-face {
  font-family: "CircularStd";
  font-weight: 400;
  font-style: italic;
  src: url("https://assets.stablemoney.in/fonts/CircularStd-BookItalic.woff")
    format("woff");
  font-display: swap;
}

@font-face {
  font-family: "CircularStd";
  font-weight: 500;
  src: url("https://assets.stablemoney.in/fonts/CircularStd-Medium.woff")
    format("woff");
  font-display: swap;
}

@font-face {
  font-family: "CircularStd";
  font-weight: 500;
  font-style: italic;
  src: url("https://assets.stablemoney.in/fonts/CircularStd-MediumItalic.woff")
    format("woff");
  font-display: swap;
}

@font-face {
  font-family: "RecklessNeue";
  font-weight: 400;
  src: url("https://assets.stablemoney.in/fonts/RecklessNeue-Book.woff2")
    format("woff2");
  font-display: swap;
}

@font-face {
  font-family: "RecklessNeue";
  font-weight: 400;
  font-style: italic;
  src: url("https://assets.stablemoney.in/fonts/RecklessNeue-BookItalic.woff2")
    format("woff2");
  font-display: swap;
}

@font-face {
  font-family: "RecklessNeue";
  font-weight: 500;
  src: url("https://assets.stablemoney.in/fonts/RecklessNeue-Medium.woff2")
    format("woff2");
  font-display: swap;
}

@font-face {
  font-family: "RecklessNeue";
  font-weight: 500;
  font-style: italic;
  src: url("https://assets.stablemoney.in/fonts/RecklessNeue-MediumItalic.woff2")
    format("woff2");
  font-display: swap;
}

:root {
  /* colors */
  --background: #ffffff;
  --foreground: #171717;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --primary-hover: #2563eb;
  --primary-active: #1d4ed8;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --secondary-hover: #e2e8f0;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --accent-hover: #e2e8f0;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --destructive-hover: #dc2626;
  --success: #10b981;
  --success-foreground: #ffffff;
  --success-hover: #059669;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --warning-hover: #d97706;
  --color-black: black;

  /* border */
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3b82f6;

  /* border-radius */
  --radius: 0.5rem;

  /* shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* animation durations */
  --duration-fast: 150ms;
  --duration-normal: 200ms;
  --duration-slow: 300ms;
  --duration-slower: 500ms;

  /* animation easings */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

[data-theme="dark"] {
  /* colors */
  --background: #0a0a0a;
  --foreground: #ededed;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --primary-hover: #60a5fa;
  --primary-active: #93c5fd;
  --secondary: #334155;
  --secondary-foreground: #f8fafc;
  --secondary-hover: #475569;
  --accent: #334155;
  --accent-foreground: #f8fafc;
  --accent-hover: #475569;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --destructive-hover: #f87171;
  --success: #10b981;
  --success-foreground: #ffffff;
  --success-hover: #34d399;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --warning-hover: #fbbf24;
  --color-black: white;

  /* border */
  --border: #334155;
  --input: #334155;
  --ring: #3b82f6;

  /* shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4),
    0 4px 6px -4px rgb(0 0 0 / 0.4);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "CircularStd";
  transition: background-color var(--duration-normal) var(--ease-in-out),
    color var(--duration-normal) var(--ease-in-out);
}

/* transitions while theme changes */
* {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-duration: var(--duration-normal);
  transition-timing-function: var(--ease-in-out);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

.animate-slide-in-up {
  animation: slideInUp var(--duration-normal) var(--ease-out);
}

.animate-slide-in-down {
  animation: slideInDown var(--duration-normal) var(--ease-out);
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-out);
}
