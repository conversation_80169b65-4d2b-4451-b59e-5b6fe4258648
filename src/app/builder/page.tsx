"use client";

import { Form<PERSON><PERSON>erCan<PERSON> } from "@/components/canvas";
import { PropertyPanel } from "@/components/canvas/property-panel";
import { useFormStore } from "@/lib/store";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { ComponentPanel } from "@/components/canvas/component-panel";

export default function BuilderPage() {
  const { isPreviewMode } = useFormStore();
  const [isComponentLibraryCollapsed, setIsComponentLibraryCollapsed] =
    useState(false);
  const [isPropertyPanelCollapsed, setIsPropertyPanelCollapsed] =
    useState(false);

  return (
    <div className="flex h-screen bg-background">
      <AnimatePresence mode="wait">
        {!isPreviewMode && (
          <motion.aside
            key="component-library"
            initial={{ x: -320, opacity: 0 }}
            animate={{
              x: 0,
              opacity: 1,
              width: isComponentLibraryCollapsed ? 48 : 320,
            }}
            exit={{ x: -320, opacity: 0 }}
            transition={{
              duration: 0.3,
              ease: [0.4, 0, 0.2, 1],
              opacity: { duration: 0.2 },
            }}
            className="border-r border-border bg-card shadow-sm flex flex-col relative"
            style={{ width: isComponentLibraryCollapsed ? "48px" : "320px" }}
          >
            <ComponentPanel
              className="flex-1 overflow-auto"
              isCollapsed={isComponentLibraryCollapsed}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() =>
                setIsComponentLibraryCollapsed(!isComponentLibraryCollapsed)
              }
              className="absolute top-4 right-2 h-8 w-8 p-0 hover:bg-muted z-10"
            >
              {isComponentLibraryCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
          </motion.aside>
        )}
      </AnimatePresence>

      <motion.main
        layout
        className="flex-1 overflow-auto bg-background"
        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
      >
        <FormBuilderCanvas />
      </motion.main>

      <AnimatePresence mode="wait">
        {!isPreviewMode && (
          <motion.aside
            key="property-panel"
            initial={{ x: 320, opacity: 0 }}
            animate={{
              x: 0,
              opacity: 1,
              width: isPropertyPanelCollapsed ? 48 : 320,
            }}
            exit={{ x: 320, opacity: 0 }}
            transition={{
              duration: 0.3,
              ease: [0.4, 0, 0.2, 1],
              opacity: { duration: 0.2 },
            }}
            className="border-l border-border bg-card shadow-sm flex flex-col relative"
            style={{ width: isPropertyPanelCollapsed ? "48px" : "320px" }}
          >
            <PropertyPanel
              className="flex-1 overflow-auto"
              isCollapsed={isPropertyPanelCollapsed}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() =>
                setIsPropertyPanelCollapsed(!isPropertyPanelCollapsed)
              }
              className="absolute top-4 left-2 h-8 w-8 p-0 hover:bg-muted z-10"
            >
              {isPropertyPanelCollapsed ? (
                <ChevronLeft className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </motion.aside>
        )}
      </AnimatePresence>
    </div>
  );
}
